import React from 'react';
import {
  Box,
  Flex,
  Text,
  HStack,
  Badge,
  Icon,
  IconButton,
  useColorModeValue
} from 'common/components';
import {
  AccordionComponent,
  PreviewSection
} from 'common/custom-components';
// import { DocumentPreviewSection } from 'common/components';
import { t } from 'i18next';
import { PrintIcon, DownloadIcon } from 'assets/svg';
import {
  getPersonalDetailsData,
  getParentGuardianDetailsData,
  getBankDetailsData,
  getCurrentCourseDetailsData,
  getPreviousAcademicDetailsData
} from '../../../common/helpers';

const ApplicationView = ({
  applicationDetails = {},
  isDetailsSuccess = false,
  showActions = true
}) => {
  const headerBg = useColorModeValue('white', 'gray.800');
  const cardBg = useColorModeValue('white', 'gray.700');

  // Get status color scheme
  const getStatusColorScheme = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'green';
      case 'applied':
      case 'submitted':
        return 'blue';
      case 'draft':
        return 'gray';
      case 'rejected':
        return 'red';
      default:
        return 'gray';
    }
  };

  // Header section with application info
  const renderHeader = () => (
    <Box
      bg="primary.500"
      borderRadius="lg"
      w="100%"
      overflow="hidden"
      mb={4}
      position="relative"
      minH={{ base: '100px', md: '150px' }}
      h={{ md: '150px' }}
    >
      {/* Mobile Layout */}
      <Box
        display={{ base: 'block', md: 'none' }}
        p={4}
        minH="100px"
        position="relative"
      >
        <Text fontSize="2xl" fontWeight="bold" color="white" mb={4}>
          {t('scholarshipApplication')}
        </Text>

        <HStack spacing={4} mb={2}>
          <Icon boxSize={5} />
          <Text fontSize="md" fontWeight="medium" color="white">
            {t('studentName')}: {applicationDetails.firstName} {applicationDetails.lastName}
          </Text>
        </HStack>

        <HStack spacing={4} mb={2}>
          <Icon boxSize={5} />
          <Text fontSize="md" fontWeight="medium" color="white">
            {t('scholarshipType')}: {applicationDetails.scholarshipType || 'Higher Secondary (HSS)'}
          </Text>
        </HStack>

        <HStack spacing={4}>
          <Icon boxSize={5} />
          <Text fontSize="md" fontWeight="medium" color="white">
            {t('applicationNumber')} / {t('status')}: {applicationDetails.applicationNumber || 'UG-1231345'}
            <Badge
              ml={3}
              px={3}
              py={1}
              borderRadius="full"
              colorScheme={getStatusColorScheme(applicationDetails.status)}
              variant="solid"
              fontSize="sm"
              fontWeight="semibold"
            >
              {applicationDetails.status || 'Approved'}
            </Badge>
          </Text>
        </HStack>
      </Box>

      {/* Desktop Layout */}
      <Flex
        direction={{ base: 'column', md: 'row' }}
        align="center"
        justify="space-between"
        display={{ base: 'none', md: 'flex' }}
        h={{ base: '100px', md: '150px' }}
        pl={6}
        pr={0}
        py={4}
        position="relative"
      >
        <Box maxW="60%" color="white">
          <Text fontSize="2xl" fontWeight="bold" color="white" mb={4}>
            {t('scholarshipApplication')}
          </Text>

          <HStack spacing={4} mb={2}>
            <Icon boxSize={5} />
            <Text fontSize="md" fontWeight="medium" color="white">
              {t('studentName')}: {applicationDetails.firstName} {applicationDetails.lastName}
            </Text>
          </HStack>

          <HStack spacing={4} mb={2}>
            <Icon boxSize={5} />
            <Text fontSize="md" fontWeight="medium" color="white">
              {t('scholarshipType')}: {applicationDetails.scholarshipType || 'Higher Secondary (HSS)'}
            </Text>
          </HStack>

          <HStack spacing={4}>
            <Icon boxSize={5} />
            <Text fontSize="md" fontWeight="medium" color="white">
              {t('applicationNumber')} / {t('status')}: {applicationDetails.applicationNumber || 'UG-1231345'}
              <Badge
                ml={3}
                px={3}
                py={1}
                borderRadius="full"
                colorScheme={getStatusColorScheme(applicationDetails.status)}
                variant="solid"
                fontSize="sm"
                fontWeight="semibold"
              >
                {applicationDetails.status || 'Approved'}
              </Badge>
            </Text>
          </HStack>
        </Box>

        {/* Right Section: Action Buttons - Positioned absolutely */}
        {showActions && (
          <Box
            position="absolute"
            right={6}
            top="50%"
            transform="translateY(-50%)"
            display={{ base: 'none', md: 'block' }}
          >
            <HStack spacing={3}>
              <IconButton
                icon={<Icon as={DownloadIcon} boxSize={5} />}
                aria-label="Download"
                size="lg"
                variant="ghost"
                color="white"
                _hover={{
                  bg: 'whiteAlpha.200',
                  transform: 'scale(1.05)'
                }}
                borderRadius="full"
              />
              <IconButton
                icon={<Icon as={PrintIcon} boxSize={5} />}
                aria-label="Print"
                size="lg"
                variant="ghost"
                color="white"
                _hover={{
                  bg: 'whiteAlpha.200',
                  transform: 'scale(1.05)'
                }}
                borderRadius="full"
              />
            </HStack>
          </Box>
        )}
      </Flex>
    </Box>
  );

  // Combine academic details for display
  const getAcademicDetailsData = () => {
    const currentCourseData = getCurrentCourseDetailsData(applicationDetails, isDetailsSuccess);
    const previousAcademicData = getPreviousAcademicDetailsData(
      applicationDetails,
      isDetailsSuccess
    );
    return [...currentCourseData, ...previousAcademicData];
  };

  // Accordion data with preview sections
  const accordionData = [
    {
      title: t('personalDetails'),
      content: (
        <PreviewSection
          data={getPersonalDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 1,
      isCompleted: false
    },
    ...(applicationDetails?.parentGuardianDetails ? [{
      title: t('parentGuardianDetails'),
      content: (
        <PreviewSection
          data={getParentGuardianDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 2,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.bankDetails ? [{
      title: t('bankDetails'),
      content: (
        <PreviewSection
          data={getBankDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 3,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.academicDetails ? [{
      title: t('academicDetails'),
      content: (
        <PreviewSection
          data={getAcademicDetailsData()}
        />
      ),
      id: 4,
      isCompleted: false
    }] : []),
    ...(applicationDetails.studentDocumentsDetails ? [{
      title: t('documentUpload'),
      content: (
        <PreviewSection
          data={[
            { label: t('documentsUploaded'), value: t('viewDocuments'), colSpan: [12, 12, 12] }
          ]}
        />
      ),
      id: 5,
      isCompleted: false
    }] : [])
  ];

  return (
    <Box p={6} bg={headerBg} minH="100vh">
      {/* Header Section */}
      {renderHeader()}

      {/* Content Sections */}
      <Box bg={cardBg} borderRadius="xl" overflow="hidden" boxShadow="lg">
        <AccordionComponent
          data={accordionData}
          allowMultiple
          currentIndexes={accordionData.map((_, index) => index)}
          isCollapsible={false}
        />
      </Box>
    </Box>
  );
};

export default ApplicationView;
