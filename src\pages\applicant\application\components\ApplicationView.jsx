import React from 'react';
import {
  Box,
  Flex,
  Text,
  HStack,
  Badge,
  Icon,
  IconButton,
  useColorModeValue,
  Image
} from 'common/components';
import {
  AccordionComponent,
  PreviewSection,
  BannerBox
} from 'common/custom-components';
// import { DocumentPreviewSection } from 'common/components';
import { t } from 'i18next';
import { PrintIcon, DownloadIcon, Pencil } from 'assets/svg';
import { BannerCorner } from 'assets/images';
import {
  getPersonalDetailsData,
  getParentGuardianDetailsData,
  getBankDetailsData,
  getCurrentCourseDetailsData,
  getPreviousAcademicDetailsData
} from '../../../common/helpers';

const ApplicationView = ({
  applicationDetails = {},
  isDetailsSuccess = false,
  showActions = true
}) => {
  const headerBg = useColorModeValue('white', 'gray.800');
  const cardBg = useColorModeValue('white', 'gray.700');

  // Get status color scheme
  const getStatusColorScheme = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'green';
      case 'applied':
      case 'submitted':
        return 'blue';
      case 'draft':
        return 'gray';
      case 'rejected':
        return 'red';
      default:
        return 'gray';
    }
  };

  // Mobile content for BannerBox
  const mobileContent = (
    <>
      <Text fontSize="2xl" fontWeight="bold" color="white" mb={4}>
        {t('scholarshipApplication')}
      </Text>

      <HStack spacing={4} mb={2}>
        <Icon boxSize={5} />
        <Text fontSize="md" fontWeight="medium" color="white">
          {t('studentName')}: {applicationDetails.firstName} {applicationDetails.lastName}
        </Text>
      </HStack>

      <HStack spacing={4} mb={2}>
        <Icon boxSize={5} />
        <Text fontSize="md" fontWeight="medium" color="white">
          {t('scholarshipType')}: {applicationDetails.scholarshipType || 'Higher Secondary (HSS)'}
        </Text>
      </HStack>

      <HStack spacing={4}>
        <Icon boxSize={5} />
        <Text fontSize="md" fontWeight="medium" color="white">
          {t('applicationNumber')} / {t('status')}: {applicationDetails.applicationNumber || 'UG-1231345'}
          <Badge
            ml={3}
            px={3}
            py={1}
            borderRadius="full"
            colorScheme={getStatusColorScheme(applicationDetails.status)}
            variant="solid"
            fontSize="sm"
            fontWeight="semibold"
          >
            {applicationDetails.status || 'Approved'}
          </Badge>
        </Text>
      </HStack>
    </>
  );

  // Desktop content for BannerBox
  const desktopContent = (
    <>
      <Text fontSize="2xl" fontWeight="bold" color="white" mb={4}>
        {t('scholarshipApplication')}
      </Text>

      <HStack spacing={4} mb={2}>
        <Icon boxSize={5} />
        <Text fontSize="md" fontWeight="medium" color="white">
          {t('studentName')}: {applicationDetails.firstName} {applicationDetails.lastName}
        </Text>
      </HStack>

      <HStack spacing={4} mb={2}>
        <Icon boxSize={5} />
        <Text fontSize="md" fontWeight="medium" color="white">
          {t('scholarshipType')}: {applicationDetails.scholarshipType || 'Higher Secondary (HSS)'}
        </Text>
      </HStack>

      <HStack spacing={4}>
        <Icon boxSize={5} />
        <Text fontSize="md" fontWeight="medium" color="white">
          {t('applicationNumber')} / {t('status')}: {applicationDetails.applicationNumber || 'UG-1231345'}
          <Badge
            ml={3}
            px={3}
            py={1}
            borderRadius="full"
            colorScheme={getStatusColorScheme(applicationDetails.status)}
            variant="solid"
            fontSize="sm"
            fontWeight="semibold"
          >
            {applicationDetails.status || 'Approved'}
          </Badge>
        </Text>
      </HStack>
    </>
  );

  // Right content (action buttons) for BannerBox
  const rightContent = showActions ? (
    <HStack spacing={3}>
      <IconButton
        icon={<Icon as={DownloadIcon} boxSize={5} />}
        aria-label="Download"
        size="lg"
        variant="ghost"
        color="white"
        _hover={{
          bg: 'whiteAlpha.200',
          transform: 'scale(1.05)'
        }}
        borderRadius="full"
      />
      <IconButton
        icon={<Icon as={PrintIcon} boxSize={5} />}
        aria-label="Print"
        size="lg"
        variant="ghost"
        color="white"
        _hover={{
          bg: 'whiteAlpha.200',
          transform: 'scale(1.05)'
        }}
        borderRadius="full"
      />
    </HStack>
  ) : null;

  // Header section with application info
  const renderHeader = () => (
    <BannerBox
      mobileContent={mobileContent}
      desktopContent={desktopContent}
      rightContent={rightContent}
      showImages
    />
  );

  // Combine academic details for display
  const getAcademicDetailsData = () => {
    const currentCourseData = getCurrentCourseDetailsData(applicationDetails, isDetailsSuccess);
    const previousAcademicData = getPreviousAcademicDetailsData(
      applicationDetails,
      isDetailsSuccess
    );
    return [...currentCourseData, ...previousAcademicData];
  };

  // Accordion data with preview sections
  const accordionData = [
    {
      title: t('personalDetails'),
      content: (
        <PreviewSection
          data={getPersonalDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 1,
      isCompleted: false
    },
    ...(applicationDetails?.parentGuardianDetails ? [{
      title: t('parentGuardianDetails'),
      content: (
        <PreviewSection
          data={getParentGuardianDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 2,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.bankDetails ? [{
      title: t('bankDetails'),
      content: (
        <PreviewSection
          data={getBankDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 3,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.academicDetails ? [{
      title: t('academicDetails'),
      content: (
        <PreviewSection
          data={getAcademicDetailsData()}
        />
      ),
      id: 4,
      isCompleted: false
    }] : []),
    ...(applicationDetails.studentDocumentsDetails ? [{
      title: t('documentUpload'),
      content: (
        <PreviewSection
          data={[
            { label: t('documentsUploaded'), value: t('viewDocuments'), colSpan: [12, 12, 12] }
          ]}
        />
      ),
      id: 5,
      isCompleted: false
    }] : [])
  ];

  return (
    <Box p={6} bg={headerBg} minH="100vh">
      {/* Header Section */}
      {renderHeader()}

      {/* Content Sections */}
      <Box bg={cardBg} borderRadius="xl" overflow="hidden" boxShadow="lg">
        <AccordionComponent
          data={accordionData}
          allowMultiple
          currentIndexes={accordionData.map((_, index) => index)}
          isCollapsible={false}
        />
      </Box>
    </Box>
  );
};

export default ApplicationView;
