import React from 'react';
import {
  Box,
  Flex,
  Text,
  VStack,
  HStack,
  Badge,
  Icon,
  IconButton,
  useColorModeValue
} from 'common/components';
import {
  AccordionComponent,
  PreviewSection
} from 'common/custom-components';
// import { DocumentPreviewSection } from 'common/components';
import { t } from 'i18next';
import { PrintIcon, DownloadIcon } from 'assets/svg';
import {
  getPersonalDetailsData,
  getParentGuardianDetailsData,
  getBankDetailsData,
  getCurrentCourseDetailsData,
  getPreviousAcademicDetailsData
} from '../../../common/helpers';

const ApplicationView = ({
  applicationDetails = {},
  isDetailsSuccess = false,
  showActions = true
}) => {
  const headerBg = useColorModeValue('white', 'gray.800');
  const cardBg = useColorModeValue('white', 'gray.700');

  // Get status color scheme
  const getStatusColorScheme = (status) => {
    switch (status?.toLowerCase()) {
      case 'approved':
        return 'green';
      case 'applied':
      case 'submitted':
        return 'blue';
      case 'draft':
        return 'gray';
      case 'rejected':
        return 'red';
      default:
        return 'gray';
    }
  };

  // Header section with application info
  const renderHeader = () => (
    <Box
      bg="linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
      color="white"
      p={6}
      borderRadius="xl"
      mb={6}
      position="relative"
      overflow="hidden"
    >
      {/* Background Pattern */}
      <Box
        position="absolute"
        top={0}
        right={0}
        bottom={0}
        width="40%"
        opacity={0.1}
        bg="whiteAlpha.100"
      />

      <Flex justify="space-between" align="flex-start" position="relative" zIndex={1}>
        <VStack align="start" spacing={4} flex={1}>
          <Text fontSize="2xl" fontWeight="bold">
            {t('scholarshipApplication')}
          </Text>

          <VStack align="start" spacing={2}>
            <HStack spacing={4}>
              <Icon boxSize={5} />
              <Text fontSize="md" fontWeight="medium">
                {t('studentName')}: {applicationDetails.firstName} {applicationDetails.lastName}
              </Text>
            </HStack>

            <HStack spacing={4}>
              <Icon boxSize={5} />
              <Text fontSize="md" fontWeight="medium">
                {t('scholarshipType')}: {applicationDetails.scholarshipType || 'Higher Secondary (HSS)'}
              </Text>
            </HStack>

            <HStack spacing={4}>
              <Icon boxSize={5} />
              <Text fontSize="md" fontWeight="medium">
                {t('applicationNumber')} / {t('status')}: {applicationDetails.applicationNumber || 'UG-1231345'}
                <Badge
                  ml={3}
                  px={3}
                  py={1}
                  borderRadius="full"
                  colorScheme={getStatusColorScheme(applicationDetails.status)}
                  variant="solid"
                  fontSize="sm"
                  fontWeight="semibold"
                >
                  {applicationDetails.status || 'Approved'}
                </Badge>
              </Text>
            </HStack>
          </VStack>
        </VStack>

        {/* Action Buttons */}
        {showActions && (
          <HStack spacing={3}>
            <IconButton
              icon={<Icon as={DownloadIcon} boxSize={5} />}
              aria-label="Download"
              size="lg"
              variant="ghost"
              color="white"
              _hover={{
                bg: 'whiteAlpha.200',
                transform: 'scale(1.05)'
              }}
              borderRadius="full"
            />
            <IconButton
              icon={<Icon as={PrintIcon} boxSize={5} />}
              aria-label="Print"
              size="lg"
              variant="ghost"
              color="white"
              _hover={{
                bg: 'whiteAlpha.200',
                transform: 'scale(1.05)'
              }}
              borderRadius="full"
            />
            <IconButton
              icon={<Text fontSize="xl">✕</Text>}
              aria-label="Close"
              size="lg"
              variant="ghost"
              color="white"
              _hover={{
                bg: 'whiteAlpha.200',
                transform: 'scale(1.05)'
              }}
              borderRadius="full"
            />
          </HStack>
        )}
      </Flex>
    </Box>
  );

  // Combine academic details for display
  const getAcademicDetailsData = () => {
    const currentCourseData = getCurrentCourseDetailsData(applicationDetails, isDetailsSuccess);
    const previousAcademicData = getPreviousAcademicDetailsData(
      applicationDetails,
      isDetailsSuccess
    );
    return [...currentCourseData, ...previousAcademicData];
  };

  // Accordion data with preview sections
  const accordionData = [
    {
      title: t('personalDetails'),
      content: (
        <PreviewSection
          data={getPersonalDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 1,
      isCompleted: false
    },
    ...(applicationDetails?.parentGuardianDetails ? [{
      title: t('parentGuardianDetails'),
      content: (
        <PreviewSection
          data={getParentGuardianDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 2,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.bankDetails ? [{
      title: t('bankDetails'),
      content: (
        <PreviewSection
          data={getBankDetailsData(applicationDetails, isDetailsSuccess)}
        />
      ),
      id: 3,
      isCompleted: false
    }] : []),
    ...(applicationDetails?.academicDetails ? [{
      title: t('academicDetails'),
      content: (
        <PreviewSection
          data={getAcademicDetailsData()}
        />
      ),
      id: 4,
      isCompleted: false
    }] : []),
    ...(applicationDetails.studentDocumentsDetails ? [{
      title: t('documentUpload'),
      content: (
        <PreviewSection
          data={[
            { label: t('documentsUploaded'), value: t('viewDocuments'), colSpan: [12, 12, 12] }
          ]}
        />
      ),
      id: 5,
      isCompleted: false
    }] : [])
  ];

  return (
    <Box p={6} bg={headerBg} minH="100vh">
      {/* Header Section */}
      {renderHeader()}

      {/* Content Sections */}
      <Box bg={cardBg} borderRadius="xl" overflow="hidden" boxShadow="lg">
        <AccordionComponent
          data={accordionData}
          allowMultiple
          currentIndexes={accordionData.map((_, index) => index)}
          isCollapsible={false}
        />
      </Box>
    </Box>
  );
};

export default ApplicationView;
